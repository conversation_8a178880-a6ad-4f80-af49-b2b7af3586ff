/* eslint-disable @typescript-eslint/no-type-alias */
import { Spin } from "@douyinfe/semi-ui";
import { Auth } from "auth";
import { redirectToDraftIfNeeded } from "components/Draft";
import {
  AnnouncementPage,
  BasicInfoContractorBlacklistAddPage,
  BasicInfoContractorBlacklistAppealPage,
  BasicInfoContractorConfigPage,
  BasicInfoContractorEmployeeBlacklistAddPage,
  BasicInfoContractorEmployeeBlacklistAppealPage,
  BasicInfoContractorEntryApplyPage,
  BasicInfoContractorEvaluationPage,
  BasicInfoContractorProjectResumptionPage,
  BasicInfoContractorProjectStopPage,
  BasicInfoContractorViolationPage,
  BasicInfoDocumentCategoryPage,
  BasicInfoDocumentInformationPage,
  CertificatePage,
  ChemicalPage,
  ContractorAccidentPage,
  ContractorCertificatePage,
  ContractorEmployeePage,
  ContractorProjectPage,
  DangerousProcessPage,
  DepartmentPage,
  EmployeePage,
  EnterpriseCertificatePage,
  EnterpriseInfoIndexPage,
  EnterpriseInfoPage,
  EquipmentPage,
  GroupPage,
  InterlockPage,
  LawRegulationPage,
  MajorHazardPage,
  MessagePage,
  MonitorIndexPage,
  MonitorPage,
  PositionPage,
  ProductionUnitPage,
  RiskManagementPage,
  RolePage,
  StorageRecordPage,
  StorageTankAreaPage,
  StorageTankPage,
  ToxicFlammableGasPage,
  WarehouseAreaPage,
  WarehousePage,
} from "pages/basicInfo";
import BigScreen from "pages/bigScreen/template";
import {
  AllocationPage,
  AreaPage,
  AwarenessPage,
  BbCheckPage,
  BbCheckTaskPage,
  BbRecordPage,
  BbStatPage,
  BbTaskPage,
  CheckRecordPage,
  ChecklistPage,
  DangerPage,
  DoubleGuardCcCategoryPage,
  DoubleGuardCcItemPage,
  DoubleGuardCcPlanPage,
  DoubleGuardCcTaskPage,
  DoubleGuardGcCheckTaskPage,
  DoubleGuardGcCheckTaskRecordPage,
  DoubleGuardGcTypeConfigPage,
  DoubleGuardIndexPage,
  EffectNewPage,
  EffectOldPage,
  EmergencyPage,
  EventPage,
  IdentificationPage,
  IncentivePage,
  MeasurePage,
  MobilePage,
  ObjectPage,
  SafetyPage,
  SnapPage,
  TaskListPage,
  UnitPage,
} from "pages/doubleGuard";
import { NotFound } from "pages/notFound";

import { useFavicon, useTitle } from "ahooks";
import { LayoutBox } from "pages/layout";
import { AuthDingTalkPage, AuthPage, LoginPage } from "pages/login";
import {
  AppointmentProcessTemplatePage,
  CodeConfigPage,
  CreateTicketPage,
  GasStandardPage,
  JobAppoimentPage,
  JsTemplateUserPage,
  PrintPreviewPage,
  ProcessTemplatePage,
  RiskMeasurePage,
  SafetyAnalysisPage,
  SafetyDisclosurePage,
  SafetyMeasurePage,
  TicketListPage,
} from "pages/ticket";
import { useEffect, useMemo, type ReactElement } from "react";
import { RouterProvider, createBrowserRouter } from "react-router-dom";
import {
  EmergencyManagementMap,
  HiddenMenu,
  MyTrainingLayoutMap,
  StudentManagementMap,
  SystemMap,
  TrainingManagementMap,
  TrainingMaterialMap,
} from "routes";
import type { ChildrenMap } from "routes/types";
import { generateLoader } from "routes/utils";
// import '@semi-bot/semi-theme-vrdemo/semi.min.css';
import { useQuery } from "@tanstack/react-query";
import { getPlatformConfig, lastPath } from "api";
import { platformConfigAtom } from "atoms";
import { base_url } from "config";
import { useAtom } from "jotai";
import { ContractorEmployeeCertificatePage } from "pages/basicInfo/contractorEmployeeCertificatePage";
import { ContractorPage } from "pages/basicInfo/contractorPage";
import { CoporateTrainingPlanPage } from "pages/coporateTraining";
import {
  InspectionIndexPage,
  InspectionPathPage,
  InspectionPlacePage,
  InspectionPlanPage,
  InspectionStandardPage,
  InspectionTaskPage,
  InspectionTaskRecordPage,
  UnscheduledTaskRecordPage,
} from "pages/intelligentInspection";
import {
  AlarmPage,
  DisplayCategoryPage,
  MonitorUnitPage,
  PersonnelAlarmPage,
  SensorAlarmPage,
  SensorPage,
} from "pages/majorHazard";
import { MapPage } from "pages/map";
import { PersonnelLocationUrlPage } from "pages/personnelLocation";
import {
  GasIntervalPage,
  JobSliceIntervalPage,
  SpecialWorkConfigPage,
} from "pages/specialWork";
import { AlarmPushConfigPage } from "pages/system";
import { VideoPage } from "pages/video";
import "remixicon/fonts/remixicon.css";
import "tdesign-react/es/style/index.css";
// import { TrainingRecordPage } from 'pages/training/recordPage';

import { SpecialWorkIndexPage } from "pages/specialWork/specialWorkIndexPage";

import { AlarmIndexPage } from "pages/alarm/alarmIndexPage";
import { DicSide } from "pages/basicInfo/content";
import { PaperResultPage } from "pages/coporateTraining/resultPage";
import EnergyXunqiaoModule from "pages/energyManagement/energyXunqiaoModule";
import EnvironmentXunqiaoModule from "pages/environmentalProtection/environmentXunqiaoModule";
import {
  EquipmentManagementDetectionPage,
  EquipmentManagementEquipmentCategoryPage,
  EquipmentManagementEquipmentPage,
  EquipmentManagementMaintenancePage,
  EquipmentManagementRepairPage,
  EquipmentManagementResumePage,
  EquipmentManagementScrapPage,
  EquipmentManagementStopPage,
} from "pages/equipmentManagement";
import { CustomizedXunqiaoTopologyPage } from "pages/fireFighter";
import FireFighterXunqiaoModule from "pages/fireFighter/fireFighterXunqiaoModule";
import {
  AIRecognitionRoutes,
  AlarmManagementRoutes,
  AuthDingtalkRoute,
  BasicRoutes,
  BigScreenRoutes,
  ContractorRoutes,
  DoubleGuardRoutes,
  EnergyManagementRoutes,
  EnvironmentManagementRoutes,
  EquipmentManagementRoutes,
  FireFighterRoutes,
  H5MapRoute,
  IntelligentInspectionRoutes,
  LoginRoute,
  MajorHazardRoutes,
  MessageRoute,
  NotPermissionRoute,
  PaperResultRoute,
  PasswordFreeRoute,
  PersonnelLocationRoutes,
  PrintPreviewRoute,
  SpecialWorkRoutes,
  VideoRoute,
} from "utils/routerConstants";

function Loading() {
  return (
    <div className="w-full h-screen flex items-center justify-center">
      <Spin size="large" />
    </div>
  );
}

/**
 * @file 异步加载组件
 */

// const SuspenseComponent = props => {
//   const {
//     Component, extraProps = {}, useLoading, importPath, importCompletesCallback, ...componentProps
//   } = props;

//   return <Suspense
//     fallback={<Loading />}>
//     <Component {...componentProps}
//       {...extraProps} />
//   </Suspense>;
// };

// const PendingComponent = (Component, extraProps = {}, useLoading, importPath) => props => {
//   return <SuspenseComponent
//     Component={Component}
//     extraProps={extraProps}
//     useLoading={useLoading}
//     importPath={importPath}
//     {...props} />;
// };

// function Pending(importFunction, extraProps = {}, useLoading = false) {
//   return PendingComponent(
//     React.lazy(() => import(importFunction)),
//     extraProps,
//     useLoading,
//     importFunction.toString(),
//   );
// }

// const lazy = (url: string) => {
//   const SettingsPage = React.lazy(() => import(url));
//   return <Suspense fallback={<Loading />}>
//     <SettingsPage />
//   </Suspense>
// }

export const RiskMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.DASHBOARD,
      element: <DoubleGuardIndexPage />,
      name: "双重预防机制",
    },
    {
      path: DoubleGuardRoutes.RISK_AREA, // "/riskArea",
      element: <AreaPage />,
      name: "风险分区管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "action1",
          name: "两单三卡",
        },
        {
          action: "action2",
          name: "简易模式复评",
        },
        {
          action: "action3",
          name: "计算模式复评",
        },
        {
          action: "action4",
          name: "评估历史",
        },
        {
          action: "action5",
          name: "重新绘制",
        },
        {
          action: "action6",
          name: "查询评估历史",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      index: true,
      element: <ObjectPage />,
      name: "风险分析对象",
      path: DoubleGuardRoutes.RISK_OBJECT, // "/riskObject",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.RISK_UNIT, // "/riskUnit",
      element: <UnitPage />,
      name: "风险分析单元",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "action1",
          name: "生成二维码",
        },
        {
          action: "export",
          name: "导出告知卡",
        },
        {
          action: "isstop",
          name: "是否停用",
        },
        {
          action: "history",
          name: "状态历史",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.RISK_EVENT, // "/riskEvent",
      element: <EventPage />,
      name: "风险分析事件",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "action1",
          name: "风险评估",
        },
        {
          action: "history",
          name: "评估历史",
        },
        {
          action: "historyAll",
          name: "查询评估历史",
        },
        {
          action: "ls",
          name: "设置LS等级区间",
        },
        {
          action: "lec",
          name: "设置LEC等级区间",
        },
        {
          action: "mes",
          name: "设置MES等级区间",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.RISK_CONTROL_MEASURE, // "/riskControlMeasure",
      element: <MeasurePage />,
      name: "风险管控措施",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
  ],
  100
);

export const ManageCardMap: ChildrenMap[] = generateLoader(
  [
    {
      index: true,
      path: DoubleGuardRoutes.AWARENESS_CARD, // "/awareness",
      element: <AwarenessPage />,
      name: "风险应知卡",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      element: <EmergencyPage />,
      name: "应急处置卡",
      path: DoubleGuardRoutes.EMERGENCY_CARD, // "/emergency",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.SAFETY_CARD, // "/safety",
      element: <SafetyPage />,
      name: "安全承诺卡",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.IDENTIFICATION_CHECKLIST, // "/identification",
      element: <IdentificationPage />,
      name: "风险辨识清单",
    },
    {
      path: DoubleGuardRoutes.CONTROL_CHECKLIST, // "/checklist",
      element: <ChecklistPage />,
      name: "风险管控清单",
    },
  ],
  200
);

export const OnlineMonitorAlertMap: ChildrenMap[] = generateLoader(
  [
    /* {
      path: MajorHazardRoutes.DASHBOARD,
      element: <MonitorIndexPage />,
      name: "重大危险源安全管理",
    }, */
    {
      path: MajorHazardRoutes.DASHBOARD,
      element: <MonitorIndexPage />,
      name: "重大危险源安全管理",
    },
    {
      path: MajorHazardRoutes.MONITOR, // "/monitor",
      element: <MonitorPage />,
      name: "视频监控信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: MajorHazardRoutes.SENSOR, // "/sensor",
      element: <SensorPage />,
      name: "实时监测信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "linechart",
          name: "历史波动图",
        },
        {
          action: "resume",
          name: "启用",
        },
        {
          action: "stop",
          name: "停用",
        },
        {
          action: "batchResume",
          name: "批量启用",
        },
        {
          action: "batchStop",
          name: "批量停用",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: MajorHazardRoutes.ALARM, // "/alarm",
      element: <AlarmPage />,
      name: "通用报警信息",
      meta: [
        {
          action: "confirm",
          name: "核实",
        },
        {
          action: "refuse",
          name: "误报",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "confirmBatch",
          name: "批量核实",
        },
        {
          action: "refuseBatch",
          name: "批量误报",
        },
      ],
    },
    {
      path: MajorHazardRoutes.SENSOR_ALARM, // "/sensor_alarm",
      element: <SensorAlarmPage />,
      name: "实时监测报警信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "alarmProcess",
          name: "处理",
        },
        {
          action: "batchAlarmProcess",
          name: "批量处理",
        },
        {
          action: "linechart",
          name: "历史波动图",
        },
      ],
    },
    {
      path: MajorHazardRoutes.PERSONNEL_ALARM, // "/personnel_alarm",
      element: <PersonnelAlarmPage />,
      name: "人员定位报警信息",
      meta: [
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "alarmProcess",
          name: "处理",
        },
        {
          action: "batchAlarmProcess",
          name: "批量处理",
        },
      ],
    },
  ],
  300
);

export const DataVisulizationMap: ChildrenMap[] = generateLoader(
  [
    {
      path: MajorHazardRoutes.DISPLAY_CATEGORY, // "/display_category",
      element: <DisplayCategoryPage />,
      name: "展示分类管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: MajorHazardRoutes.MONITOR_UNIT, // "/monitor_unit",
      element: <MonitorUnitPage />,
      name: "监测单元信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  400
);

export const EnterpriseCertificateMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.CERTIFICATE, // "/certificate",
      element: <EnterpriseCertificatePage />,
      name: "企业证书信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      path: BasicRoutes.LAW_REGULATION, // "/law_regulation",
      element: <LawRegulationPage />,
      name: "法律法规标准",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  500
);

export const EnterpriseInformationMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.ENTERPRISE_INFO_DASHBOARD,
      element: <EnterpriseInfoIndexPage />,
      name: "安全管理基础信息",
    },
    {
      path: BasicRoutes.ENTERPRISE_INFO, // "/enterprise_info",
      element: <EnterpriseInfoPage />,
      name: "企业信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.ENTERPRISE_AREA, // "/enterprise_area",
      element: <AreaPage />,
      name: "企业区域管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "action1",
          name: "两单三卡",
        },
        {
          action: "action2",
          name: "简易模式复评",
        },
        {
          action: "action3",
          name: "计算模式复评",
        },
        {
          action: "action4",
          name: "评估历史",
        },
        {
          action: "action5",
          name: "重新绘制",
        },
        {
          action: "action6",
          name: "查询评估历史",
        },
      ],
    },
    {
      path: BasicRoutes.RISK_MANAGEMENT, // "/risk_management",
      element: <RiskManagementPage />,
      name: "企业风险研判",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "approve",
          name: "审批",
        },
      ],
    },
    {
      path: BasicRoutes.ANNOUNCEMENT, // "/announcement",
      element: <AnnouncementPage />,
      name: "公告管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  600
);

export const ProductiveProcessMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.MAJOR_HAZARD, // "/major_hazard",
      element: <MajorHazardPage />,
      name: "重大危险源信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "evaluate",
          name: "等级评定",
        },
      ],
    },
    {
      path: BasicRoutes.TOXIC_FLAMMABLE_GAS, // "/toxic_flammable_gas",
      element: <ToxicFlammableGasPage />,
      name: "有毒可燃气体信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.DANGEROUS_PROCESS, // "/dangerous_process",
      element: <DangerousProcessPage />,
      name: "危险化工工艺信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.PRODUCTION_UNIT, // "/production_unit",
      element: <ProductionUnitPage />,
      name: "生产装置信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.STORAGE_TANK_AREA, // "/storage_tank_area",
      element: <StorageTankAreaPage />,
      name: "储罐区信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.STORAGE_TANK, // "/storage_tank",
      element: <StorageTankPage />,
      name: "储罐信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.WAREHOUSE_AREA, // "/warehouse_area",
      element: <WarehouseAreaPage />,
      name: "仓库区信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.WAREHOUSE, // "/warehouse",
      element: <WarehousePage />,
      name: "库房信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.CHEMICAL, // "/chemical",
      element: <ChemicalPage />,
      name: "化学品信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.STORAGE_RECORD, // "/storage_record",
      element: <StorageRecordPage />,
      name: "化学品出入库记录",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.INTERLOCK, // "/interlock",
      element: <InterlockPage />,
      name: "联锁信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  600
);

export const EquipmentFacilitiesMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.EQUIPMENT_CATEGORY,
      element: <EquipmentManagementEquipmentCategoryPage />,
      name: "设备类型",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
      ],
    },
    {
      path: BasicRoutes.EQUIPMENT, // "/equipment",
      element: <EquipmentPage />,
      name: "设备信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  700
);

export const HumanResourceMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.DEPARTMENT, // "/department",
      element: <DepartmentPage />,
      name: "部门管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      element: <PositionPage />,
      name: "岗位管理",
      path: BasicRoutes.POSITION, // "/position",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "action1",
          name: "批量新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.EMPLOYEE, // "/employee",
      element: <EmployeePage />,
      name: "员工管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "batchActive",
          name: "批量激活",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      path: BasicRoutes.ROLE, // "/role",
      element: <RolePage />,
      name: "角色管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "action1",
          name: "设置权限",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.GROUP, // "/group",
      element: <GroupPage />,
      name: "工作组管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.EMPLOYEE_CERTIFICATE, // "/employee_certificate",
      element: <CertificatePage />,
      name: "人员证书信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  1000
);

export const BasicContractorMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.CONTRACTOR, // "/contractor",
      element: <ContractorPage />,
      name: "承包商基础信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        {
          action: "addBlacklist",
          name: "加入黑名单",
        },
        {
          action: "removeBlacklist",
          name: "移出黑名单",
        },
        {
          action: "share",
          name: "邀请完善信息",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.CONTRACTOR_CERTIFICATE, // "/contractor_certificate",
      element: <ContractorCertificatePage />,
      name: "承包商资证信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.CONTRACTOR_EMPLOYEE, // "/contractor_employee",
      element: <ContractorEmployeePage />,
      name: "承包商人员信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "addBlacklist",
          name: "加入黑名单",
        },
        {
          action: "removeBlacklist",
          name: "移出黑名单",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.CONTRACTOR_EMPLOYEE_CERTIFICATE, // "/contractor_employee_certificate",
      element: <ContractorEmployeeCertificatePage />,
      name: "承包商人员证书信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.CONTRACTOR_PROJECT, // "/contractor_project",
      element: <ContractorProjectPage />,
      name: "承包商项目管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.CONTRACTOR_ACCIDENT, // "/contractor_accident",
      element: <ContractorAccidentPage />,
      name: "承包商事故事件",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  1000
);

export const DocumentManagmentMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.DOCUMENT_CATEGORY,
      element: <BasicInfoDocumentCategoryPage />,
      name: "文档类型",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: BasicRoutes.DOCUMENT_INFORMATION,
      element: <BasicInfoDocumentInformationPage />,
      name: "文档信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  1000
);

export const SafetyMeasureMap: ChildrenMap[] = generateLoader(
  [
    {
      element: <SafetyAnalysisPage />,
      name: "作业安全分析库",
      path: SpecialWorkRoutes.SAFETY_ANALYSIS, // "/safety_analysis",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      element: <SafetyMeasurePage />,
      name: "作业安全措施库",
      path: SpecialWorkRoutes.SAFETY_MEASURE, // "/safety_measure",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      element: <RiskMeasurePage />,
      name: "风险辨识措施库",
      path: SpecialWorkRoutes.RISK_MEASURE, // "/risk_measure",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      element: <GasStandardPage />,
      name: "气体采样分析库",
      path: SpecialWorkRoutes.GAS_STANDARD, // "/gas_standard",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      element: <SafetyDisclosurePage />,
      name: "安全交底清单",
      path: SpecialWorkRoutes.SAFETY_DISCLOSURE, // "/safety_disclosure",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  1100
);

export const GovSupervisionMap: ChildrenMap[] = generateLoader(
  [
    {
      element: <DoubleGuardGcTypeConfigPage />,
      name: "专项检查配置",
      path: DoubleGuardRoutes.GOV_SUPERVISION_SETTINGS,
      meta: [
        {
          action: "edit",
          name: "编辑设置",
        },
      ],
    },
    {
      element: <DoubleGuardGcCheckTaskPage />,
      name: "专项任务下发",
      path: DoubleGuardRoutes.GOV_SUPERVISION_CHECK_TASK,
      meta: [
        {
          action: "edit",
          name: "编辑下发",
        },
      ],
    },
    {
      element: <DoubleGuardGcCheckTaskRecordPage />,
      name: "专项检查记录",
      path: DoubleGuardRoutes.GOV_SUPERVISION_CHECK_RECORD,
      meta: [
        {
          action: "dispatch",
          name: "任务分解",
        },
        {
          action: "finish",
          name: "任务完成",
        },
        {
          action: "danger",
          name: "隐患录入",
        },
      ],
    },
  ],
  1100
);

export const JobTmplMap: ChildrenMap[] = generateLoader(
  [
    {
      element: <JobAppoimentPage />,
      name: "作业预约",
      path: SpecialWorkRoutes.JOB_APPOIMENT, // "/job_appoiment",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "view",
          name: "查看",
        },
        {
          action: "approve",
          name: "审批",
        },
      ],
    },
    {
      element: <TicketListPage />,
      name: "作业票",
      path: SpecialWorkRoutes.JOB_TMPL_LIST, // "/job_tmpl_list",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "view",
          name: "查看打印",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      element: <CreateTicketPage />,
      name: "创建作业票",
      hide: true,
      path: `${SpecialWorkRoutes.JOB_TMPL}/:cid/:id`,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  1200
);

export const Ticket: ChildrenMap[] = generateLoader(
  [
    {
      path: SpecialWorkRoutes.DASHBOARD, // "/sw_config",
      element: <SpecialWorkIndexPage />,
      name: "作业管理",
    },
    {
      path: SpecialWorkRoutes.SW_CONFIG, // "/sw_config",
      element: <SpecialWorkConfigPage />,
      name: "作业票通用配置",
    },
    {
      path: SpecialWorkRoutes.JS_TEMPLATE_USER, // "/js_template_user",
      element: <JsTemplateUserPage />,
      name: "作业票模板配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: `${SpecialWorkRoutes.APPOINTMENT_PROCESS_TEMPLATE}`,
      element: <AppointmentProcessTemplatePage />,
      name: "作业预约配置",
      meta: [
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: SpecialWorkRoutes.CODE_CONFIG, // "/code_config",
      element: <CodeConfigPage />,
      name: "作业票编码配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: `${SpecialWorkRoutes.PROCESS_TEMPLATE}/:id`, // "/process_template/:id",
      element: <ProcessTemplatePage />,
      hide: true,
      name: "作业票审批配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SpecialWorkRoutes.GAS_INTERVAL, // "/gas_interval",
      element: <GasIntervalPage />,
      name: "气体检测配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SpecialWorkRoutes.JOB_SLICE_INTERVAL, // "/job_slice_interval",
      element: <JobSliceIntervalPage />,
      name: "作业票有效期",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  1300
);

export const BbTaskMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.BB_TEMPLATE, // "/bb_template",
      element: <BbTaskPage />,
      name: "包保责任模板",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_CHECK, // "/bb_check",
      element: <BbCheckPage />,
      name: "包保排查计划",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_TASK, // "/bb_task",
      element: <BbCheckTaskPage />,
      name: "包保排查任务",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_RECORD, // "/bb_record",
      element: <BbRecordPage />,
      name: "包保排查记录",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_STAT, // "/bb_stat",
      element: <BbStatPage />,
      name: "履职统计",
    },
  ],
  300
);

export const IncentiveMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.INCENTIVE, // "/incentive",
      element: <IncentivePage />,
      name: "额外奖惩管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.EFFECT_OLD, // "/effect_old",
      element: <EffectOldPage />,
      name: "机制运行效果(旧)",
    },
    {
      path: DoubleGuardRoutes.EFFECT_NEW, // "/effect_new",
      element: <EffectNewPage />,
      name: "机制运行效果(新)",
    },
  ],
  500
);

export const CheckPlanMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.IMEI, // "/imei",
      element: <MobilePage />,
      name: "IMEI管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.ALLOCATION, // "/allocation",
      element: <AllocationPage />,
      name: "排查计划",
      meta: [
        {
          action: "action1",
          name: "批量分配",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.TASK_LIST, // "/task_list",
      element: <TaskListPage />,
      name: "排查任务",
      meta: [
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.CHECK_RECORD, // "/check_record",
      element: <CheckRecordPage />,
      name: "排查记录",
    },
    {
      path: DoubleGuardRoutes.DANGER, // "/danger",
      element: <DangerPage />,
      name: "隐患治理",
      meta: [
        {
          action: "create1",
          name: "新增即查即改隐患",
        },
        {
          action: "create2",
          name: "新增限期整改隐患",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "allowUpload",
          name: "允许上报",
        },
        {
          action: "batchAllowUpload",
          name: "批量允许上报",
        },
      ],
    },
  ],
  400
);

export const PersonnelLocationMap: ChildrenMap[] = generateLoader(
  [
    {
      path: PersonnelLocationRoutes.PERSONNEL_LOCATION, // "/personnel_location",
      element: <PersonnelLocationUrlPage />,
      name: "人员定位管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
  ],
  500
);

export const AIMap: ChildrenMap[] = generateLoader(
  [
    {
      path: AIRecognitionRoutes.VIDEO_AI, // "/video_ai",
      element: <AlarmPage filter={{ type: 5 }} />,
      name: "视频监控AI分析",
      meta: [
        {
          action: "confirm",
          name: "核实",
        },
        {
          action: "refuse",
          name: "误报",
        },
        {
          action: "confirmBatch",
          name: "批量核实",
        },
        {
          action: "refuseBatch",
          name: "批量误报",
        },
      ],
    },
  ],
  600
);

export const IntelligentInspectionMap: ChildrenMap[] = generateLoader(
  [
    {
      path: IntelligentInspectionRoutes.INSPECTION_DASHBOARD,
      element: <InspectionIndexPage />,
      name: "智能巡检",
    },
    {
      path: IntelligentInspectionRoutes.INSPECTION_STANDARD, // "/inspection_standard",
      element: <InspectionStandardPage />,
      name: "巡检标准管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      path: IntelligentInspectionRoutes.INSPECTION_PLACE, // "/inspection_place",
      element: <InspectionPlacePage />,
      name: "巡检点",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "stopCheck",
          name: "停检",
        },
        {
          action: "resumeCheck",
          name: "恢复检查",
        },
        {
          action: "batchStopCheck",
          name: "批量停检",
        },
        {
          action: "batchResumeCheck",
          name: "批量恢复检查",
        },
        {
          action: "exportQRCode",
          name: "导出二维码",
        },
      ],
    },
    {
      path: IntelligentInspectionRoutes.INSPECTION_PATH, // "/inspection_path",
      element: <InspectionPathPage />,
      name: "巡检路径规划",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "stopCheck",
          name: "停检",
        },
        {
          action: "batchStopCheck",
          name: "批量停检",
        },
        {
          action: "resumeCheck",
          name: "恢复检查",
        },
        {
          action: "batchResumeCheck",
          name: "批量恢复检查",
        },
      ],
    },
    {
      path: IntelligentInspectionRoutes.INSPECTION_PLAN, // "/inspection_plan",
      element: <InspectionPlanPage />,
      name: "巡检计划管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: IntelligentInspectionRoutes.INSPECTION_TASK, // "/inspection_task",
      element: <InspectionTaskPage />,
      name: "巡检任务",
      meta: [
        /* {
        action: 'create',
        name: '新增'
      },  */
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        /* {
        action: 'edit',
        name: '编辑'
      } */
      ],
    },
    {
      path: IntelligentInspectionRoutes.INSPECTION_TASK_RECORD, // "/inspection_task_record",
      element: <InspectionTaskRecordPage />,
      name: "巡检任务执行记录",
      meta: [
        /* {
        action: 'create',
        name: '新增'
      }, */
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        /* {
        action: 'edit',
        name: '编辑'
      } */
      ],
    },
    {
      path: IntelligentInspectionRoutes.UNSCHEDULED_TASK_RECORD, // "/unscheduled_task_record",
      element: <UnscheduledTaskRecordPage />,
      name: "非计划巡检执行记录",
      meta: [
        /* {
        action: 'create',
        name: '新增'
      }, */
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        /* {
        action: 'edit',
        name: '编辑'
      } */
      ],
    },
  ],
  500
);

export const EquipmentArchiveMap: ChildrenMap[] = generateLoader(
  [
    {
      path: EquipmentManagementRoutes.EQUIPMENT_CATEGORY,
      element: <EquipmentManagementEquipmentCategoryPage />,
      name: "设备类型",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
      ],
    },
    {
      path: EquipmentManagementRoutes.EQUIPMENT,
      element: <EquipmentManagementEquipmentPage />,
      name: "设备信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        {
          action: "maintenance",
          name: "保养",
        },
        {
          action: "detection",
          name: "检测",
        },
        {
          action: "repair",
          name: "维修",
        },
        {
          action: "stop",
          name: "停用",
        },
        {
          action: "resume",
          name: "恢复",
        },
        {
          action: "scrap",
          name: "报废",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);

export const EquipmentManagementMap: ChildrenMap[] = generateLoader(
  [
    {
      path: EquipmentManagementRoutes.EQUIPMENT_MAINTAIN,
      element: <EquipmentManagementMaintenancePage />,
      name: "设备保养",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      path: EquipmentManagementRoutes.EQUIPMENT_DETECTION,
      element: <EquipmentManagementDetectionPage />,
      name: "设备检测",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备维修
    {
      path: EquipmentManagementRoutes.EQUIPMENT_REPAIR,
      element: <EquipmentManagementRepairPage />,
      name: "设备维修",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备停用
    {
      path: EquipmentManagementRoutes.EQUIPMENT_STOP,
      element: <EquipmentManagementStopPage />,
      name: "设备停用",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备恢复
    {
      path: EquipmentManagementRoutes.EQUIPMENT_RESUME,
      element: <EquipmentManagementResumePage />,
      name: "设备恢复",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    //设备报废
    {
      path: EquipmentManagementRoutes.EQUIPMENT_SCRAP,
      element: <EquipmentManagementScrapPage />,
      name: "设备报废",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);

export const FireFighterModuleMap: ChildrenMap[] = generateLoader(
  [
    {
      path: FireFighterRoutes.CUSTOMIZE_XUNQIAO_TOPOLOGY,
      element: <CustomizedXunqiaoTopologyPage />,
      name: "管网图",
    },
    {
      path: FireFighterRoutes.CUSTOMIZE_XUNQIAO_CHART,
      element: <FireFighterXunqiaoModule />,
      name: "消防图表",
    },
  ],
  600
);

export const EnergyManagementMap: ChildrenMap[] = generateLoader(
  [
    {
      // path: EnergyManagementRoutes.DUMMY, // 旧的占位符路径
      // 使用一个基础路径，例如 '/energy-management/*'
      // React Router v6 使用 '/*' 来表示匹配该路径及其所有子路径
      // 具体路径应与您在路由配置中为能源管理模块设定的父路径一致
      // path: EnergyManagementRoutes.ROOT + "/*", // 例如 "/energy-management/*"
      path: EnergyManagementRoutes.CUSTOMIZE_XUNQIAO_CHART,
      element: <EnergyXunqiaoModule />, // 使用新的包装组件
      name: "能源管理",
      // meta 和其他属性根据需要保留或调整
    },
  ],
  600 // 这个数字参数的意义根据 generateLoader 的实现来定
);

export const EnvironmentManagementMap: ChildrenMap[] = generateLoader(
  [
    // 新增环保动态模块的路由
    {
      path: EnvironmentManagementRoutes.CUSTOMIZE_XUNQIAO_CHART,
      element: <EnvironmentXunqiaoModule />,
      name: "环保动态",
      // 如果需要 loader，可以参照其他模块添加，例如：
      // loader: () => redirectToDraftIfNeeded(RoleKeyPath.EnvironmentalProtection), // RoleKeyPath.EnvironmentalProtection 可能需要您在 config/index.ts 中定义
    },
  ],
  600
);

export const SnapMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.SNAP, // "/snap",
      element: <SnapPage />,
      name: "随手拍",
      meta: [
        {
          action: "examine",
          name: "审核",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);

export const EnterpriseSelfInspectionMap: ChildrenMap[] = generateLoader([
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_CATEGORY,
    element: <DoubleGuardCcCategoryPage />,
    name: "检查类别",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_ITEM,
    element: <DoubleGuardCcItemPage />,
    name: "排查库",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_PLAN,
    element: <DoubleGuardCcPlanPage />,
    name: "排查计划",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_TASK,
    element: <DoubleGuardCcTaskPage />,
    name: "排查任务",
    meta: [
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
]);

export const ContractorBasicMap: ChildrenMap[] = generateLoader([
  {
    path: ContractorRoutes.CONTRACTOR_CONFIG,
    element: <BasicInfoContractorConfigPage />,
    name: "承包商配置",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR,
    element: <ContractorPage />,
    name: "承包商基础信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "detail",
        name: "查看详情",
      },
      {
        action: "addBlacklist",
        name: "加入黑名单",
      },
      {
        action: "removeBlacklist",
        name: "移出黑名单",
      },
      {
        action: "share",
        name: "邀请完善信息",
      },
      {
        action: "import",
        name: "导入",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_CERTIFICATE,
    element: <ContractorCertificatePage />,
    name: "承包商资证信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE, // "/contractor_employee",
    element: <ContractorEmployeePage />,
    name: "承包商人员信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "addBlacklist",
        name: "加入黑名单",
      },
      {
        action: "removeBlacklist",
        name: "移出黑名单",
      },
      {
        action: "import",
        name: "导入",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE_CERTIFICATE, // "/contractor_employee_certificate",
    element: <ContractorEmployeeCertificatePage />,
    name: "承包商人员证书信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "import",
        name: "导入",
      },
    ],
  },
]);

export const ContractorEntryMap: ChildrenMap[] = generateLoader([
  {
    path: ContractorRoutes.CONTRACTOR_ENTRY_TRAINING,
    element: <CoporateTrainingPlanPage filter={{ objectType: 3 }} />,
    name: "承包商培训",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "detail",
        name: "查看详情",
      },
      {
        action: "share",
        name: "分享",
      },
      {
        action: "revokeEntry",
        name: "取消入厂资格",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_ENTRY_APPLY,
    element: <BasicInfoContractorEntryApplyPage />,
    name: "承包商入厂申请",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审核",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
]);

export const ContractorProjectMap: ChildrenMap[] = generateLoader([
  {
    element: (
      <TicketListPage
        filter={{ unitCategory: 2 }} // 承包商
        isInContractorModule={true}
      />
    ),
    name: "承包商作业票",
    path: ContractorRoutes.JOB_TMPL_LIST, // "/job_tmpl_list",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "view",
        name: "查看打印",
      },
      {
        action: "export",
        name: "导出",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_PROJECT, // "/contractor_project",
    element: <ContractorProjectPage />,
    name: "承包商项目管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "stop",
        name: "停工",
      },
      {
        action: "batchStop",
        name: "批量停工",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_ACCIDENT, // "/contractor_accident",
    element: <ContractorAccidentPage />,
    name: "承包商事故事件",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_VIOLATION,
    element: <BasicInfoContractorViolationPage />,
    name: "承包商违规事件",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_PROJECT_STOP,
    element: <BasicInfoContractorProjectStopPage />,
    name: "承包商项目停工记录",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_PROJECT_RESUMPTION,
    element: <BasicInfoContractorProjectResumptionPage />,
    name: "承包商项目复工管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审核",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
]);

export const ContractorEvaluationMap: ChildrenMap[] = generateLoader([
  {
    path: ContractorRoutes.CONTRACTOR_EVALUATION,
    element: <BasicInfoContractorEvaluationPage />,
    name: "承包商业绩评价",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_BLACKLIST_ADD,
    element: <BasicInfoContractorBlacklistAddPage />,
    name: "承包商黑名单记录",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_BLACKLIST_APPEAL,
    element: <BasicInfoContractorBlacklistAppealPage />,
    name: "承包商黑名单申诉管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审批",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE_BLACKLIST_ADD,
    element: <BasicInfoContractorEmployeeBlacklistAddPage />,
    name: "承包商人员黑名单记录",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE_BLACKLIST_APPEAL,
    element: <BasicInfoContractorEmployeeBlacklistAppealPage />,
    name: "承包商人员黑名单申诉管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审批",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
]);

export const AlarmSettingsMap: ChildrenMap[] = generateLoader(
  [
    {
      path: AlarmManagementRoutes.ALARM_DASHBOARD,
      element: <AlarmIndexPage />,
      name: "报警管理",
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_SENSOR, // "/sensor",
      element: <SensorPage />,
      name: "监测指标",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "linechart",
          name: "历史波动图",
        },
        {
          action: "resume",
          name: "启用",
        },
        {
          action: "stop",
          name: "停用",
        },
        {
          action: "batchResume",
          name: "批量启用",
        },
        {
          action: "batchStop",
          name: "批量停用",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_RULES,
      element: <AlarmPushConfigPage />,
      name: "报警规则配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_MONITORTYPE,
      element: <DicSide itemId={4} />,
      name: "监控类型配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_REASON,
      element: <DicSide itemId={5} />,
      name: "报警原因配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_MEASURE,
      element: <DicSide itemId={6} />,
      name: "报警处置措施配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  2000
);

export const AlarmProcessMap: ChildrenMap[] = generateLoader(
  [
    {
      path: AlarmManagementRoutes.ALARM_PROCESS_SENSOR_ALARM,
      element: <SensorAlarmPage />,
      name: "实时报警",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "alarmProcess",
          name: "处理",
        },
        {
          action: "batchAlarmProcess",
          name: "批量处理",
        },
        {
          action: "linechart",
          name: "历史波动图",
        },
      ],
    },
  ],
  2000
);

export const AlarmAnalysisMap: ChildrenMap[] = generateLoader(
  [
    {
      path: "",
      element: null,
      name: "实时监控",
    },
  ],
  2000
);

export const MessageMap: ChildrenMap[] = generateLoader(
  [
    {
      path: MessageRoute, // "/message",
      element: <MessagePage />,
      name: "消息中心",
    },
  ],
  2000
);

const RoleRouter = [
  ...RiskMap,
  ...HumanResourceMap,
  ...ManageCardMap,
  ...BbTaskMap,
  ...CheckPlanMap,
  ...IncentiveMap,
  ...SnapMap,
  ...GovSupervisionMap,
  ...MessageMap,
  ...Ticket,
  ...SafetyMeasureMap,
  ...JobTmplMap,
  ...OnlineMonitorAlertMap,
  ...DataVisulizationMap,
  ...EnterpriseCertificateMap,
  ...ProductiveProcessMap,
  ...EquipmentFacilitiesMap,
  ...EmergencyManagementMap,
  ...BasicContractorMap,
  ...DocumentManagmentMap,
  ...SystemMap,
  ...PersonnelLocationMap,
  ...AIMap,
  ...IntelligentInspectionMap,
  ...TrainingMaterialMap,
  ...TrainingManagementMap,
  ...StudentManagementMap,
  ...MyTrainingLayoutMap,
  ...EnterpriseInformationMap,
  ...EquipmentArchiveMap,
  ...EquipmentManagementMap,
  ...FireFighterModuleMap,
  ...EnergyManagementMap,
  ...EnvironmentManagementMap,
  ...EnterpriseSelfInspectionMap,
  ...ContractorBasicMap,
  ...ContractorEntryMap,
  ...ContractorProjectMap,
  ...ContractorEvaluationMap,
  ...AlarmSettingsMap,
  ...AlarmProcessMap,
  ...AlarmAnalysisMap,
  ...HiddenMenu,
];

// const BigScreen = Pending('./pages/bigScreen/template/index.tsx');

const createBaseRoter = () => [
  {
    // index: true,
    path: LoginRoute, // "/login",
    element: <LoginPage />,
    name: "登录",
  },
  {
    path: PasswordFreeRoute, // "/auth",
    element: <AuthPage />,
    name: "免密登录",
  },
  {
    path: AuthDingtalkRoute, // "/auth/dingtalk",
    element: <AuthDingTalkPage />,
    name: "钉钉登陆",
  },
  {
    path: `${PrintPreviewRoute}/:id`, // "/print_preview/:id",
    element: <PrintPreviewPage />,
  },
  {
    path: BigScreenRoutes.HOME, // "/big_screen",
    element: <BigScreen />,
    name: "一张图",
  },
  {
    path: VideoRoute, // "/video",
    element: <VideoPage />,
    name: "监控播放",
  },
  {
    path: `${PaperResultRoute}/:id`, // "/paper_result/:id"
    element: <PaperResultPage />,
    name: "考试结果",
  },
  {
    path: `${H5MapRoute}/:type`, // "/h5_map/:type",
    element: <MapPage />,
    name: "一张图",
  },
  {
    path: NotPermissionRoute,
    element: <NotFound statusCode={403} autoRedirect={false} />,
    name: "403",
  },
  {
    path: "/",
    element: <LayoutBox />,
    children: RoleRouter,
  },

  /* {
      index: true,
      path: "home",
      element: <DashBoard />,
    }, */
  {
    path: "*",
    element: <NotFound />,
  },
];

export default function App(): ReactElement {
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);

  const { data: serverPlatformConfig } = useQuery({
    queryKey: ["getPlatformConfig"],
    queryFn: async () => getPlatformConfig(),
  });
  const serverPlatformConfigOptions = useMemo(
    () => serverPlatformConfig?.data ?? {},
    [serverPlatformConfig]
  );

  useEffect(() => {
    if (serverPlatformConfigOptions.platformTitle) {
      setPlatformConfig({
        ...serverPlatformConfigOptions,
        id: "custom",
        display_name: serverPlatformConfigOptions.platformTitle,
        logo_name: base_url + serverPlatformConfigOptions.icon,
        departmentRootName: serverPlatformConfigOptions.departmentRootName,
        webMenus: serverPlatformConfigOptions.webMenus,
        webUrlPrefix: serverPlatformConfigOptions.webUrlPrefix,
        cesiumUris: serverPlatformConfigOptions.cesiumUris,
      });
    }
  }, [serverPlatformConfigOptions]);

  useFavicon(platformConfig.logo_name);
  useTitle(platformConfig.display_name);
  /* useFavicon(getCurrentOem()?.logo_name);
  useTitle(getCurrentOem()?.display_name) */
  return (
    <Auth
      onLogin={(user) => {
        redirectToDraftIfNeeded(user.employee, () => {
          let replacePath = lastPath.get() ? lastPath.get() : "/";
          window.location.replace(replacePath);
        });
      }}
      defaultMenu={RoleRouter}
    >
      <RouterProvider router={createBrowserRouter(createBaseRoter())} />
    </Auth>
  );
}
