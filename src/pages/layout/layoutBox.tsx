/* eslint-disable import/prefer-default-export */
import {
  <PERSON><PERSON>,
  Badge,
  Banner,
  Dropdown,
  Layout,
  Nav,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useAuth } from "@reactivers/hooks";
import { useQuery } from "@tanstack/react-query";
import { getSystemInfo, getUnreadMsgCount } from "api";
import { globalRole, platformConfigAtom } from "atoms";
import dayjs from "dayjs";
import { mainMenuMap, useMenuHooks, useReloadAppHooks } from "hooks";
import { useAtom } from "jotai";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  Link,
  matchPath,
  Outlet,
  useLocation,
  useNavigate,
} from "react-router-dom";
import { formatDate, getTourist, logoutTourist } from "utils";
import {
  BigScreenRoutes,
  hiddenModuleItemList,
  SystemSettingsRoutes,
} from "utils/routerConstants";

import AppQrCode from "./AppQrCode";
import { DashBoard } from "./dashBoard";
import { SvgIcon1, SvgIcon3, SvgIcon4 } from "./icon";
import { ResetPwd } from "./resetPwd";
import { TabNav } from "./tabNav";

const NotRole = () => {
  return (
    <div>
      <Banner
        type="danger"
        description={<div>对不起，暂无该功能您暂无权限，请联系管理员开通</div>}
      />
    </div>
  );
};

export const HeaderRight = ({ isHome = false }) => {
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);
  const { user, logout } = useAuth();
  const [visible, setVisible] = useState(false);
  const navigate = useNavigate();

  const unreadMessage = useQuery({
    queryKey: ["getUnreadMsgCount"],
    queryFn: async () => getUnreadMsgCount(user.userInfo?.id),
    refetchInterval: 1000 * 60 * 1,
    enabled: !!user.userInfo?.id,
  });
  const unreadMessageCount = useMemo(() => {
    // return unreadMessage.data?.data?.publicMessageUnreadCount || 0 + unreadMessage.data?.data?.workMessageUnreadCount || 0;
    return unreadMessage.data?.data?.totalUnreadCount;
  }, [unreadMessage.data?.data]);

  const handleResetPwd = useCallback(() => {
    setVisible(!visible);
  }, [setVisible, visible]);

  const msgElm = (
    <Tooltip content="消息">
      <Link
        to="/message"
        className={
          isHome
            ? "w-[32px] h-[32px] rounded bg-[#31A7FF] opacity-40 hover:opacity-100 text-center transition-all flex justify-center items-center"
            : "w-[32px] h-[32px] rounded  transition-all flex justify-center items-center"
        }
      >
        <SvgIcon3
          className={
            isHome
              ? "w-6 cursor-pointer [&]:fill-white"
              : "w-6 cursor-pointer [&]:fill-black/70"
          }
        />
      </Link>
    </Tooltip>
  );

  return (
    <>
      <ResetPwd visible={visible} onClose={handleResetPwd} logout={logout} />

      {platformConfig?.webMenus?.includes(
        mainMenuMap.filter((item) => {
          return item.key === "big_screen";
        })[0].bindApiKey
      ) ? (
        <Tooltip content="大屏一张图">
          <Link
            to={BigScreenRoutes.HOME}
            className={
              isHome
                ? "w-[32px] h-[32px] rounded bg-[#31A7FF] opacity-40 hover:opacity-100 text-center transition-all flex justify-center items-center"
                : "w-[32px] h-[32px] rounded text-center transition-all flex justify-center items-center"
            }
          >
            <SvgIcon1
              className={
                isHome
                  ? "w-6 cursor-pointer [&]:fill-white"
                  : "w-6 cursor-pointer [&]:fill-black/70"
              }
            />
          </Link>
        </Tooltip>
      ) : null}
      <AppQrCode type={isHome ? "link" : "button"} />

      {getTourist() ? null : (
        <>
          {unreadMessageCount > 0 ? (
            <Badge type="warning" count={unreadMessageCount}>
              {msgElm}
            </Badge>
          ) : (
            msgElm
          )}
        </>
      )}

      {getTourist() ? (
        <Dropdown
          trigger={"hover"}
          showTick
          position="bottomRight"
          render={
            <Dropdown.Menu>
              <Dropdown.Item
                type="tertiary"
                onClick={() => navigate(SystemSettingsRoutes.ABOUT)}
              >
                关于系统
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item
                type="danger"
                onClick={() => {
                  logoutTourist();
                  logout();
                }}
              >
                退出登录
              </Dropdown.Item>
            </Dropdown.Menu>
          }
        >
          <p className="cursor-pointer w-[32px] h-[32px] rounded bg-[#31A7FF] opacity-40 hover:opacity-100 transition-all text-center flex justify-center items-center">
            <SvgIcon4 className="w-6 cursor-pointer [&]:fill-white" />
          </p>
        </Dropdown>
      ) : (
        <Dropdown
          trigger={"hover"}
          showTick
          position="bottomRight"
          render={
            <Dropdown.Menu>
              <Dropdown.Title>
                <p className="text-sm text-black">
                  {user?.userInfo?.name}(ID: {user?.userInfo?.id})
                </p>
              </Dropdown.Title>
              <Dropdown.Title type="primary">
                工号: {user?.userInfo?.employeeId}
              </Dropdown.Title>
              <Dropdown.Divider />
              <Dropdown.Item type="primary" onClick={handleResetPwd}>
                修改密码
              </Dropdown.Item>
              <Dropdown.Item
                type="tertiary"
                onClick={() => navigate(SystemSettingsRoutes.ABOUT)}
              >
                关于系统
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item
                type="danger"
                onClick={() => {
                  logoutTourist();
                  logout();
                }}
              >
                退出登录
              </Dropdown.Item>
            </Dropdown.Menu>
          }
        >
          {isHome ? (
            <p className="cursor-pointer w-[32px] h-[32px] rounded bg-[#31A7FF] opacity-40 hover:opacity-100 transition-all text-center flex justify-center items-center">
              <SvgIcon4 className="w-6 cursor-pointer [&]:fill-white" />
            </p>
          ) : (
            <Avatar color="orange" size="small" className="mx-2">
              {(user.username ?? "").charAt(0).toUpperCase()}
            </Avatar>
          )}
        </Dropdown>
      )}
    </>
  );
};
export function LayoutBox() {
  useReloadAppHooks();
  // useCheckTrainingRecordsHooks();
  const [role] = useAtom(globalRole);
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);
  const [openKeys, setOpenKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);

  const [visible, setVisible] = useState(false);
  const {
    DoubleGuardRoutes,
    SpecialWorkRoutes,
    HideMenuRoutes,
    BaseSettingRoutes,
    SystemSettingRoutes,
    MajorHazardRoutes,
    EmergencyManagementRoutes,
    PersonnelLocationRoutes,
    AIRoutes,
    IntelligentInspectionRoutes,
    TrainingRoutes,
    EquipmentManagementRoutes,
    FireFighterModuleRoutes,
    EnergyModuleRoutes,
    EnvironmentModuleRoutes,
    ContractorModuleRoutes,
    AlarmModuleRoutes,
    MainMenu,
  } = useMenuHooks();

  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { Header, Footer, Sider, Content } = Layout;
  const { data } = useQuery({
    queryKey: ["getSystemInfo"],
    queryFn: getSystemInfo,
  });

  useEffect(() => {
    if (data?.data?.currentTime) {
      const now = dayjs();
      const is = dayjs().isSame(now, "minutes");
      if (!is) {
        const options = {
          content: `用户本地时间[${formatDate(now)}]与服务器时间[${formatDate(data.data.currentTime)}]相差过大,请校准本地时间!`,
          duration: 3,
        };
        Toast.error(options);
      }
    }
  }, [data]);

  const location = useLocation();
  const { pathname } = location;

  const expandMenu = (restdRoutes: any) => {
    for (let index = 0; index < restdRoutes.length; index++) {
      const has = () => {
        return restdRoutes[index].items.some((item) =>
          item?.itemKey === pathname ? true : matchPath(item?.itemKey, pathname)
        );
      };
      if (
        restdRoutes[index].items
          ? has()
          : restdRoutes[index]?.itemKey === pathname
      ) {
        const restdRoutesHideDash = [];
        restdRoutes.forEach((o) => {
          const _items = [];
          o?.items?.forEach?.((i) => {
            // if (!i.itemKey.includes(BasicDashBoardSuffix)) {
            if (
              !hiddenModuleItemList.some((item) => i.itemKey.includes(item))
            ) {
              _items.push(i);
            }
          });
          restdRoutesHideDash.push({
            ...o,
            items: _items,
          });
        });

        return restdRoutesHideDash;
      }
    }
  };

  const SecondaryMenu = useMemo(() => {
    const routers = [
      DoubleGuardRoutes,
      SpecialWorkRoutes,
      BaseSettingRoutes,
      SystemSettingRoutes,
      HideMenuRoutes,
      MajorHazardRoutes,
      EmergencyManagementRoutes,
      PersonnelLocationRoutes,
      AIRoutes,
      IntelligentInspectionRoutes,
      TrainingRoutes,
      EquipmentManagementRoutes,
      FireFighterModuleRoutes,
      EnergyModuleRoutes,
      EnvironmentModuleRoutes,
      ContractorModuleRoutes,
      AlarmModuleRoutes,
    ];
    const oriData = () => {
      let menu = null;
      routers.forEach((i) => {
        const is = expandMenu(i);
        if (is) {
          menu = expandMenu(i);
        }
      });

      if (menu?.length) {
        return menu;
      }

      // 移除HumanResourceMap检查，因为已拆分到basicinfo模块

      return MainMenu.find((item) => item?.itemKey === pathname) || [];
    };

    // 二级菜单下如果没有子菜单直接隐藏, 没有子菜单，本身就是最后一层子菜单，直接显示
    return oriData()?.filter?.((o) => o.items?.length || o.level === 1) ?? [];
  }, [pathname, DoubleGuardRoutes]);

  const handleClickMenu = (e) => {
    if (e.openKeys) {
      setOpenKeys(e.openKeys);
    }
    if (e.text) {
      navigate(e.itemKey);
      setSelectedKeys([e.itemKey]);
    }
  };
  useEffect(() => {
    const secondaryMenuKey = SecondaryMenu
      ? SecondaryMenu.find((item) =>
          item.items
            ? item.items.some((child) => child.itemKey === pathname)
            : item.itemKey === pathname
        )?.itemKey
      : null;

    if (secondaryMenuKey) {
      setOpenKeys([secondaryMenuKey]);
    }
    if (!selectedKeys.includes(pathname)) {
      setSelectedKeys([pathname]);
    }
    console.log(secondaryMenuKey, "secondaryMenuKeysecondaryMenuKey");
  }, [SecondaryMenu, pathname]);

  const unreadMessage = useQuery({
    queryKey: ["getUnreadMsgCount"],
    queryFn: async () => getUnreadMsgCount(user.userInfo?.id),
    refetchInterval: 1000 * 60 * 1,
    enabled: !!user.userInfo?.id,
  });

  // const unreadMessageCount = useMemo(() => unreadMessage.data?.data?.publicMessageUnreadCount || 0 + unreadMessage.data?.data?.workMessageUnreadCount || 0, [unreadMessage.data?.data?.publicMessageUnreadCount, unreadMessage.data?.data?.workMessageUnreadCount]);
  const unreadMessageCount = useMemo(
    () => unreadMessage.data?.data?.totalUnreadCount,
    [unreadMessage.data?.data?.totalUnreadCount]
  );
  const searchParams = new URLSearchParams(location.search);
  const isLayout = searchParams.get("layout");

  const handleResetPwd = useCallback(() => {
    setVisible(!visible);
  }, [setVisible, visible]);

  const RoleMainMenu = useMemo(() => {
    const tmp: any = [];
    MainMenu.forEach((o) => {
      const list: any = [];

      o.items.forEach((i) => {
        if (!i.hide) {
          list.push(i);
        }
      });
      tmp.push({
        icon: o.icon,
        items: list,
      });
    });

    return tmp;
  }, [MainMenu]);

  if (pathname == "/") {
    return <DashBoard menu={RoleMainMenu?.[0]?.items} />;
  }

  if (isLayout == 1) {
    return (
      <div className="h-screen p-2 w-full">
        <Outlet />
      </div>
    );
  }

  return (
    <Layout className="rounded-border border-slate-200 h-screen">
      <Header style={{ backgroundColor: "var(--semi-color-bg-1)" }}>
        <Nav
          mode="horizontal"
          header={{
            logo: (
              <img
                src={platformConfig.logo_name}
                onClick={() => {
                  navigate("/");
                }}
              />
            ),
            text: (
              <a
                onClick={() => {
                  navigate("/");
                }}
              >
                {platformConfig.display_name}
              </a>
            ),
          }}
          items={RoleMainMenu}
          onClick={handleClickMenu}
          footer={
            <div className="flex gap-x-2">
              <HeaderRight />
            </div>
          }
        />
      </Header>
      <Layout className="h-[calc(100%-122px)]">
        {SecondaryMenu.length > 0 ? (
          <Sider className="bg-[color:var(--semi-color-bg-1)] h-full">
            <Nav
              footer={{
                collapseButton: true,
              }}
              className="max-w-[220px] h-full"
              limitIndent={false}
              openKeys={openKeys}
              selectedKeys={selectedKeys}
              items={SecondaryMenu}
              onClick={handleClickMenu}
            />
          </Sider>
        ) : null}

        <Layout className="h-full">
          <TabNav />

          <Content className="p-3 bg-[#f8f9fa] h-[calc(100%-45px)] overflow-auto">
            {role ? <Outlet /> : <NotRole />}
          </Content>
        </Layout>
      </Layout>
      <Footer className="flex justify-between items-center bg-[color:var(--semi-grey-0)] p-[20px] text-[color:var(--semi-color-text-2)] text-sm">
        <span className="flex items-center">
          <img className="w-[20px] mr-2" src={platformConfig?.logo_name} />
          <span>
            Copyright © {dayjs().format("YYYY")} &nbsp;{" "}
            {platformConfig.display_name} All Rights Reserved.{" "}
          </span>
        </span>
        {/* <span>
            <span style={{ marginRight: '24px' }}>平台客服</span>
            <span>反馈建议</span>
            </span> */}
      </Footer>
    </Layout>
  );
}
