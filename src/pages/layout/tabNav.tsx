import {
  IconClose,
  IconRefresh,
  IconTreeTriangleRight,
  IconUnlink,
} from "@douyinfe/semi-icons";
import { Modal, TabPane, Tabs, Tooltip } from "@douyinfe/semi-ui";
import { messageFnAtom } from "atoms/basicInfo";
import {
  allocationFnAtom,
  areaFnAtom,
  awarenessFnAtom,
  bbCheckFnAtom,
  bbRecordFnAtom,
  bbTaskFnAtom,
  checkRecordFnAtom,
  checklistFnAtom,
  dangerFnAtom,
  emergencyFnAtom,
  eventFnAtom,
  identificationFnAtom,
  incentiveFnAtom,
  measureFnAtom,
  objectFnAtom,
  safetyFnAtom,
  snapFnAtom,
  unitFnAtom,
} from "atoms/doubleGuard";
import { globalTabNav, routerDraft<PERSON>tom } from "atoms/global";
import { useAtom } from "jotai";
import { get } from "lodash-es";
import { uniq } from "ramda";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { matchPath, useLocation, useNavigate } from "react-router-dom";
import {
  EmergencyManagementMap,
  HiddenMenu,
  MyTrainingLayoutMap,
  StudentManagementMap,
  SystemMap,
  TrainingManagementMap,
  TrainingMaterialMap,
} from "routes";
import {
  AIMap,
  AlarmAnalysisMap,
  AlarmProcessMap,
  AlarmSettingsMap,
  BasicContractorMap,
  BbTaskMap,
  CheckPlanMap,
  ContractorBasicMap,
  ContractorEntryMap,
  ContractorEvaluationMap,
  ContractorProjectMap,
  DataVisulizationMap,
  DocumentManagmentMap,
  EnergyManagementMap,
  EnterpriseCertificateMap,
  EnterpriseInformationMap,
  EnterpriseSelfInspectionMap,
  EnvironmentManagementMap,
  EquipmentArchiveMap,
  EquipmentFacilitiesMap,
  EquipmentManagementMap,
  FireFighterModuleMap,
  GovSupervisionMap,
  HumanResourceMap,
  IncentiveMap,
  IntelligentInspectionMap,
  JobTmplMap,
  ManageCardMap,
  MessageMap,
  OnlineMonitorAlertMap,
  PersonnelLocationMap,
  ProductiveProcessMap,
  RiskMap,
  SafetyMeasureMap,
  SnapMap,
  Ticket,
} from "../../App";

export const TabNav = () => {
  const [routerDraft, setRouterDraft] = useAtom(routerDraftAtom);
  const location = useLocation();
  const navigate = useNavigate();
  const [tabNav, setTabNav] = useAtom(globalTabNav);
  const [pathStack, setPathStack] = useState(["/"]);

  const { pathname } = location;
  const [refreshObject] = useAtom(objectFnAtom);
  const [refreshArea] = useAtom(areaFnAtom);
  const [refreshUnit] = useAtom(unitFnAtom);
  const [refreshEvent] = useAtom(eventFnAtom);
  // const [refreshHistory] = useAtom(historyFnAtom);
  const [refreshMeasure] = useAtom(measureFnAtom);
  const [refreshAwareness] = useAtom(awarenessFnAtom);
  const [refreshEmergency] = useAtom(emergencyFnAtom);
  const [refreshSafety] = useAtom(safetyFnAtom);
  const [refreshChecklist] = useAtom(checklistFnAtom);
  const [refreshIdentification] = useAtom(identificationFnAtom);
  const [refreshBbTask] = useAtom(bbTaskFnAtom);
  const [refreshBbCheck] = useAtom(bbCheckFnAtom);
  const [refreshBbRecord] = useAtom(bbRecordFnAtom);
  // const [refreshMobile] = useAtom(mobileFnAtom);
  const [refreshAllocation] = useAtom(allocationFnAtom);
  const [refreshDanger] = useAtom(dangerFnAtom);
  const [refreshCheckRecord] = useAtom(checkRecordFnAtom);
  const [refreshIncentive] = useAtom(incentiveFnAtom);
  const [refreshSnap] = useAtom(snapFnAtom);
  const [refreshMessage] = useAtom(messageFnAtom);

  const mapRouteToRefreshFn = {
    "/riskObject": refreshObject,
    "/riskArea": refreshArea,
    "/riskUnit": refreshUnit,
    "/riskEvent": refreshEvent,
    // refreshHistory,
    "/riskControlMeasure": refreshMeasure,
    "/awareness": refreshAwareness,
    "/emergenc": refreshEmergency,
    "/safety": refreshSafety,
    "/checklist": refreshChecklist,
    "/identification": refreshIdentification,
    "/bb_task": refreshBbTask,
    "/bb_check": refreshBbCheck,
    "/bb_record": refreshBbRecord,
    // refreshMobile,
    "/allocation": refreshAllocation,
    "/danger": refreshDanger,
    "/check_record": refreshCheckRecord,
    "/incentive": refreshIncentive,
    "/snap": refreshSnap,
    "/message": refreshMessage,
  };

  useEffect(() => {
    let path = pathname;
    // const splitPath = pathname?.split('/')
    // if (splitPath?.length >=3) {
    //   path = `/${splitPath[1]}`
    // }
    setPathStack(uniq([...pathStack, path]).filter((o) => o != "/"));
  }, [location]);

  const handleChangeTab = useCallback(
    (type: number) => {
      const curIndex = pathStack.indexOf(pathname);
      let activeKey = "";
      if (type > 0) {
        activeKey =
          curIndex + 1 < pathStack.length
            ? pathStack[curIndex + 1]
            : pathStack[0];
      } else {
        activeKey =
          curIndex - 1 >= 0
            ? pathStack[curIndex - 1]
            : pathStack[pathStack.length - 1];
      }
      navigate(activeKey);
    },
    [navigate, pathname, pathStack]
  );

  const joinMap = useMemo(() => {
    let routers = [
      {
        path: "/",
        name: "首页",
      },
      ...RiskMap,
      ...HumanResourceMap,
      ...ManageCardMap,
      ...BbTaskMap,
      ...CheckPlanMap,
      ...IncentiveMap,
      ...GovSupervisionMap,
      ...SnapMap,
      ...MessageMap,
      ...SafetyMeasureMap,
      ...JobTmplMap,
      ...Ticket,
      ...BasicContractorMap,
      ...DocumentManagmentMap,
      ...SystemMap,
      ...OnlineMonitorAlertMap,
      ...DataVisulizationMap,
      ...EnterpriseCertificateMap,
      ...ProductiveProcessMap,
      ...EquipmentFacilitiesMap,
      ...EmergencyManagementMap,
      ...EnterpriseInformationMap,
      ...IntelligentInspectionMap,
      ...TrainingMaterialMap,
      ...TrainingManagementMap,
      ...StudentManagementMap,
      ...MyTrainingMap,
      ...MyTrainingLayoutMap,
      ...PersonnelLocationMap,
      ...AIMap,
      ...EquipmentArchiveMap,
      ...EquipmentManagementMap,
      ...FireFighterModuleMap,
      ...EnergyManagementMap,
      ...EnvironmentManagementMap,
      ...EnterpriseSelfInspectionMap,
      ...ContractorBasicMap,
      ...ContractorEntryMap,
      ...ContractorProjectMap,
      ...ContractorEvaluationMap,
      ...AlarmSettingsMap,
      ...AlarmProcessMap,
      ...AlarmAnalysisMap,
      ...HiddenMenu,
    ];
    return routers;
  }, []);

  const handleChangeTabCloseOthers = useCallback(() => {
    setPathStack(uniq([pathname]));
  }, [pathname]);

  const handleChangeTabCloseAll = useCallback(() => {
    navigate("/");
  }, []);

  const handleCloseTab = useCallback(
    (tabKey: string) => {
      const nav = () => {
        const index = pathStack.indexOf(tabKey);
        const next = pathStack[index - 1] || pathStack[index + 1] || "/";
        setPathStack(pathStack.filter((path) => path !== tabKey));
        if (tabKey === pathname) {
          navigate(next);
        }
      };
      if (routerDraft) {
        Modal.warning({
          title: "您确定要离开页面？",
          content: "页面中存在未保存内容，请检查!",
          okText: "离开",
          okType: "danger",
          onOk: () => {
            setRouterDraft(true);
            nav();
          },
        });
      } else {
        nav();
      }
    },
    [pathStack, routerDraft]
  );
  const handleRefreshTab = useCallback(() => {
    if (
      mapRouteToRefreshFn[pathname] &&
      typeof mapRouteToRefreshFn[pathname].refetch === "function"
    ) {
      mapRouteToRefreshFn[pathname].refetch();
    }
  }, [pathname]);

  const tabRef = useRef<typeof Tabs>(null);

  useEffect(() => {
    const fn =
      typeof requestIdleCallback === "function"
        ? requestIdleCallback
        : setTimeout;
    const identifier = fn(() => {
      // https://github.com/DouyinFE/semi-design/issues/1367
      // 自动滚动到选中的 Tab
      if (get(tabRef, "current.contentRef.current.previousSibling")) {
        const scrollList =
          tabRef.current.contentRef.current.previousSibling.querySelector(
            `.semi-overflow-list-scroll-wrapper`
          );
        const current = scrollList.children[pathStack.indexOf(pathname)];
        current.scrollIntoView({ behavior: "smooth", block: "end" });
        if (current) {
          current.click();
        }
      }
    });
    return () => {
      if (typeof cancelIdleCallback === "function") {
        cancelIdleCallback(identifier);
      } else {
        clearTimeout(identifier);
      }
    };
  }, [pathStack, pathname]);

  return (
    <>
      <Tabs
        ref={tabRef}
        type="card"
        activeKey={pathname}
        defaultActiveKey="/"
        onChange={(activeKey) => {
          navigate(activeKey ?? "/");
        }}
        onTabClose={handleCloseTab}
        className="pt-2 pr-3"
        contentStyle={{
          padding: 0,
        }}
        collapsible
        preventScroll
        tabBarExtraContent={
          <div className="flex gap-3">
            <Tooltip content="上一个">
              <IconTreeTriangleRight
                className="cursor-pointer rotate-180"
                onClick={() => {
                  handleChangeTab(-1);
                }}
              />
            </Tooltip>
            <Tooltip content="下一个">
              <IconTreeTriangleRight
                className="cursor-pointer"
                onClick={() => {
                  handleChangeTab(1);
                }}
              />
            </Tooltip>
            <Tooltip content="刷新">
              <IconRefresh
                className="cursor-pointer"
                onClick={handleRefreshTab}
              />
            </Tooltip>
            <Tooltip content="关闭其他" position="topRight">
              <IconUnlink
                className="cursor-pointer"
                onClick={handleChangeTabCloseOthers}
              />
            </Tooltip>
            <Tooltip content="关闭所有" position="topRight">
              <IconClose
                className="cursor-pointer"
                onClick={handleChangeTabCloseAll}
              />
            </Tooltip>
          </div>
        }
      >
        {pathStack.map((t) => (
          <TabPane
            closable={t !== "/"}
            tab={joinMap.find(({ path = "" }) => matchPath(path, t))?.name}
            itemKey={t}
            key={t}
          />
        ))}
      </Tabs>
    </>
  );
};
