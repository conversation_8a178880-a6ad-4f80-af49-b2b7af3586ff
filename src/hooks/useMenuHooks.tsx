import { IconDesktop } from "@douyinfe/semi-icons";
import { useLocalStorage } from "@reactivers/hooks";
import { useQuery } from "@tanstack/react-query";
import { getEmployee } from "api";
import { getEnergyManagementMenu } from "api/energy_management/xunqiao";
import { getEnvironmentProtectionMenu } from "api/environment_protection/xunqiao";
import { getFireProtectionMenu } from "api/fireFighter/xunqiao";
import { platformConfigAtom } from "atoms";
import { RoleKeyCode, RoleKeyPath } from "config";
import { useAtom } from "jotai";
import { SvgIcon1 } from "pages/layout/icon";
import { find, propEq } from "ramda";
import { useMemo } from "react";
import type { ChildrenMap } from "routes/types";
import { getAdmins } from "utils";
import { userInfoNameInLocalStorage } from "utils/constants";
import {
  AIMap,
  AlarmAnalysisMap,
  AlarmProcessMap,
  AlarmSettingsMap,
  BasicContractorMap,
  BbTaskMap,
  CheckPlanMap,
  ContractorBasicMap,
  ContractorEntryMap,
  ContractorEvaluationMap,
  ContractorProjectMap,
  DataVisulizationMap,
  DocumentManagmentMap,
  EmergencyManagementMap,
  EnterpriseCertificateMap,
  EnterpriseInformationMap,
  EnterpriseSelfInspectionMap,
  EquipmentArchiveMap,
  EquipmentFacilitiesMap,
  EquipmentManagementMap,
  GovSupervisionMap,
  HiddenMenu,
  HumanResourceMap,
  IncentiveMap,
  IntelligentInspectionMap,
  JobTmplMap,
  ManageCardMap,
  MyTrainingLayoutMap,
  OnlineMonitorAlertMap,
  PersonnelLocationMap,
  ProductiveProcessMap,
  RiskMap,
  SafetyMeasureMap,
  SnapMap,
  StudentManagementMap,
  SystemMap,
  Ticket,
  TrainingManagementMap,
  TrainingMaterialMap,
} from "../App";

type Roles = {
  role: Array<{
    menuCode: string;
    permissions: string[];
  }>;
  isAdmin: boolean;
};

export const mainMenuMap = [
  /* {
    itemKey: HiddenMenu?.[0]?.path,
    text: '系统配置(仅内部使用)',
    icon: <IconPriceTag size="large" />,
  }, */
  {
    // itemKey: SystemSettingRoutes?.[0]?.items?.[0]?.itemKey,
    // itemKey: generateItemKey(SystemSettingRoutes),
    text: "系统管理基础信息",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    icon1: "/images/home/<USER>",
    // module: SystemSettingRoutes,
    // hide: hasHide(SystemSettingRoutes),
    bindApiKey: 1,
    key: "system_setting",
    isInMenu: true,
  },
  {
    // itemKey: '/human_resource', //BaseSettingRoutes?.[0]?.items?.[0]?.itemKey,
    // itemKey: generateItemKey(BaseSettingRoutes),
    text: "安全管理基础信息",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: BaseSettingRoutes,
    // hide: hasHide(BaseSettingRoutes),
    bindApiKey: 2,
    key: "base_setting",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(DoubleGuardRoutes),
    text: "双重预防机制",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: DoubleGuardRoutes,
    // hide: hasHide(DoubleGuardRoutes),
    bindApiKey: 3,
    key: "double_guard",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(SpecialWorkRoutes),
    text: "作业管理",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: SpecialWorkRoutes,
    // hide: hasHide(SpecialWorkRoutes),
    bindApiKey: 4,
    key: "special_work",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(IntelligentInspectionRoutes),
    text: "智能巡检",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: IntelligentInspectionRoutes,
    // hide: hasHide(IntelligentInspectionRoutes),
    bindApiKey: 9,
    key: "intelligent_inspection",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(TrainingRoutes),
    text: "教育培训",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: TrainingRoutes,
    // hide: hasHide(TrainingRoutes),
    bindApiKey: 10,
    key: "training",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(EquipmentManagementRoutes),
    text: "设备管理",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: EquipmentManagementRoutes,
    // hide: hasHide(EquipmentManagementRoutes),
    bindApiKey: 12,
    key: "equipment",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(MajorHazardRoutes),
    text: "重大危险源",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    icon1: "/images/home/<USER>",
    // module: MajorHazardRoutes,
    // hide: hasHide(MajorHazardRoutes),
    bindApiKey: 5,
    key: "major_hazard",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(EmergencyManagementRoutes),
    text: "应急管理",
    icon1: "/images/home/<USER>",
    icon: (
      <img src="/images/home/<USER>" className="w-5 h-5" />
    ),
    // module: EmergencyManagementRoutes,
    // hide: hasHide(EmergencyManagementRoutes),
    bindApiKey: 6,
    key: "emergency_management",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(PersonnelLocationRoutes),
    text: "人员定位",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: PersonnelLocationRoutes,
    // hide: hasHide(PersonnelLocationRoutes),
    bindApiKey: 7,
    key: "personnel_location",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(AIRoutes),
    text: "AI智能分析",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: AIRoutes,
    // hide: hasHide(AIRoutes),
    bindApiKey: 8,
    key: "ai",
    isInMenu: true,
  },
  {
    text: "消防管理",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: MajorHazardRoutes,
    // hide: hasHide(MajorHazardRoutes),
    bindApiKey: 13,
    key: "fire_fighter",
    isInMenu: true,
  },
  {
    text: "能源管理",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: MajorHazardRoutes,
    // hide: hasHide(MajorHazardRoutes),
    bindApiKey: 14,
    key: "energy",
    isInMenu: true,
  },
  {
    text: "环保管理",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: MajorHazardRoutes,
    // hide: hasHide(MajorHazardRoutes),
    bindApiKey: 15,
    key: "environment",
    isInMenu: true,
  },
  {
    text: "承包商管理",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    // module: MajorHazardRoutes,
    // hide: hasHide(MajorHazardRoutes),
    bindApiKey: 16,
    key: "contractor",
    isInMenu: true,
  },
  {
    text: "报警管理",
    icon1: "/images/home/<USER>",
    icon: <img src="/images/home/<USER>" className="w-5 h-5" />,
    bindApiKey: 17,
    key: "alert_management",
    isInMenu: true,
  },
  {
    // itemKey: generateItemKey(MainMenuAllRoutes),
    text: "大屏展示",
    icon1: <SvgIcon1 className="w-6 cursor-pointer [&]:fill-black/70" />,
    icon: <SvgIcon1 className="w-6 cursor-pointer [&]:fill-black/70" />,
    bindApiKey: 11,
    key: "big_screen",
    isInMenu: false,
  },
];

export const useMenuHooks = () => {
  // const nav = useNavigate();
  const [platformConfig, setPlatformConfig] = useAtom(platformConfigAtom);

  const { getItem } = useLocalStorage(userInfoNameInLocalStorage);
  const id = getItem()?.userInfo?.id;
  const employeeId = getItem()?.userInfo?.employeeId;
  const { data } = useQuery({
    queryKey: [`getRole`],
    queryFn: () => {
      return getEmployee(id);
    },
    enabled: Boolean(id),
  });
  const routerToMenu = (list: ChildrenMap, roleItem: Roles): any[] => {
    const pathname = window.location.pathname;

    const temporary = [];
    for (const o of list) {
      if (!o?.hide) {
        // 现在的code是根据path查找的
        const item = find(propEq(o[RoleKeyPath], RoleKeyCode))(roleItem.role);
        if (roleItem.isAdmin) {
          temporary.push({
            text: o.name,
            level: 1,
            itemKey: o?.[RoleKeyPath] ?? o?.itemKey,
            permissions: o?.loader?.() ?? [],
            serverPermissions: item?.permissions ?? [],
          });
        } else if (item?.[RoleKeyCode] || pathname === "/hidden/permission") {
          // 路径为/hidden/permission 的时候，需要跳过验证,不然无法自检
          temporary.push({
            text: o.name,
            level: 1,
            itemKey: o?.[RoleKeyPath] ?? o?.itemKey,
            permissions: o?.loader?.() ?? [],
            serverPermissions: item?.permissions ?? [],
          });
        }
      }
    }

    return temporary;
  };
  const list = useMemo(() => {
    const permission = data?.data?.role?.permission
      ? data?.data?.role?.permission
      : "[]";
    return {
      role: JSON.parse(permission),
      isAdmin: getAdmins(employeeId),
    };
  }, [data]);

  /* const { data: systemInfo } = useQuery({
    queryKey: ['getSystemInfo'],
    queryFn: () => getSystemInfo(),
  })
  const systemInfoOptions = useMemo(() => {
    return systemInfo?.data ?? {}
  }, [systemInfo]) */

  const SpecialWorkRoutes = useMemo(
    () => [
      {
        text: "作业配置",
        itemKey: "job_config",
        items: routerToMenu(Ticket, list),
      },
      {
        text: "作业信息",
        itemKey: "safety_measure",
        items: routerToMenu(SafetyMeasureMap, list),
      },
      {
        text: "作业管理",
        itemKey: "job_tmpl",
        items: routerToMenu(JobTmplMap, list),
      },
    ],
    [list]
  );

  const HideMenuRoutes = useMemo(
    () => [
      {
        text: "系统隐藏菜单",
        itemKey: "form_config",
        items: routerToMenu(HiddenMenu, list),
      },
    ],
    []
  );

  const DoubleGuardRoutes = useMemo(
    () => [
      {
        text: "风险分级管控",
        itemKey: "risk",
        items: routerToMenu(RiskMap, list),
      },
      {
        text: "两单三卡",
        itemKey: "managecard",
        items: routerToMenu(ManageCardMap, list),
      },
      {
        text: "包保责任",
        itemKey: "bbtask",
        items: routerToMenu(BbTaskMap, list),
      },
      {
        text: "隐患排查治理",
        itemKey: "checkpan",
        items: routerToMenu(CheckPlanMap, list),
      },
      {
        text: "激励约束机制",
        itemKey: "incentive",
        items: routerToMenu(IncentiveMap, list),
      },
      {
        text: "政府专项",
        itemKey: "government_supervision",
        items: routerToMenu(GovSupervisionMap, list),
      },
      {
        text: "企业自查",
        itemKey: "enterprise_self_inspection",
        items: routerToMenu(EnterpriseSelfInspectionMap, list),
      },
      ...routerToMenu(SnapMap, list),
    ],
    [list]
  );

  const MajorHazardRoutes = useMemo(
    () => [
      {
        text: "在线监测预警",
        itemKey: "monitor",
        items: routerToMenu(OnlineMonitorAlertMap, list),
      },
      {
        text: "数据可视化",
        itemKey: "visualization",
        items: routerToMenu(DataVisulizationMap, list),
      },
    ],
    [list]
  );

  const IntelligentInspectionRoutes = useMemo(
    () => [
      {
        text: "智能巡检",
        itemKey: "intelligent_inspection_reop",
        items: routerToMenu(IntelligentInspectionMap, list),
      },
    ],
    [list]
  );

  const TrainingRoutes = useMemo(
    () => [
      {
        text: "学习资料",
        itemKey: "training",
        items: routerToMenu(TrainingMaterialMap, list),
      },
      {
        text: "培训管理",
        itemKey: "training_management",
        items: routerToMenu(TrainingManagementMap, list),
      },
      {
        text: "学员管理",
        itemKey: "student_management",
        items: routerToMenu(StudentManagementMap, list),
      },
      {
        text: "我的培训",
        itemKey: "my_training",
        items: routerToMenu(MyTrainingLayoutMap, list),
      },
    ],
    [list]
  );

  const EmergencyManagementRoutes = useMemo(
    () => [
      {
        text: "应急管理",
        itemKey: "emergency_management",
        items: routerToMenu(EmergencyManagementMap, list),
      },
    ],
    [list]
  );

  const BaseSettingRoutes = useMemo(
    () => [
      {
        text: "企业基础信息",
        itemKey: "risk_management",
        items: routerToMenu(EnterpriseInformationMap, list),
      },
      {
        text: "安全生产相关证照",
        itemKey: "enterprise_certificate",
        items: routerToMenu(EnterpriseCertificateMap, list),
      },
      {
        text: "生产过程基础信息",
        itemKey: "productive_process",
        items: routerToMenu(ProductiveProcessMap, list),
      },
      {
        text: "设备设施基础信息",
        itemKey: "equipment and facilities",
        items: routerToMenu(EquipmentFacilitiesMap, list),
      },
      {
        text: "企业人员基础信息",
        itemKey: "human_resource",
        items: routerToMenu(HumanResourceMap, list),
      },
      {
        text: "第三方人员基础信息",
        itemKey: "contractor",
        items: routerToMenu(BasicContractorMap, list),
      },
      {
        text: "文档管理",
        itemKey: "document_management",
        items: routerToMenu(DocumentManagmentMap, list),
      },
    ],
    [list]
  );

  const SystemSettingRoutes = useMemo(
    () => [
      {
        text: "系统管理",
        itemKey: "system_setting",
        items: routerToMenu(SystemMap, list),
      },
    ],
    [list]
  );

  const PersonnelLocationRoutes = useMemo(
    () => [
      {
        text: "人员定位",
        itemKey: "personnel_location",
        items: routerToMenu(PersonnelLocationMap, list),
      },
    ],
    [list]
  );

  const AIRoutes = useMemo(
    () => [
      {
        text: "AI智能分析",
        itemKey: "ai_analysis",
        items: routerToMenu(AIMap, list),
      },
    ],
    [list]
  );

  const EquipmentManagementRoutes = useMemo(
    () => [
      {
        text: "设备档案",
        itemKey: "equipment_archive",
        items: routerToMenu(EquipmentArchiveMap, list),
      },
      {
        text: "设备管理",
        itemKey: "equipment_management",
        items: routerToMenu(EquipmentManagementMap, list),
      },
    ],
    [list]
  );

  const { data: fireProtectionMenuData } = useQuery({
    queryKey: ["getFireProtectionMenu"],
    queryFn: getFireProtectionMenu,
  });

  const FireFighterModuleRoutes = useMemo(() => {
    return createDynamicModuleRoutes(
      "fire_fighter",
      fireProtectionMenuData,
      routerToMenu,
      list,
      [{ text: "管网图", name: "管网图", staticId: 0 }]
    );
  }, [list, fireProtectionMenuData, routerToMenu]);

  const { data: EnergyMenuData } = useQuery({
    queryKey: ["getEnergyManagementMenu"],
    queryFn: getEnergyManagementMenu,
  });

  const EnergyModuleRoutes = useMemo(() => {
    return createDynamicModuleRoutes(
      "energy",
      EnergyMenuData,
      routerToMenu,
      list
    );
  }, [list, EnergyMenuData, routerToMenu]);

  const { data: EnvironmentMenuData } = useQuery({
    queryKey: ["getEnvironmentManagementMenu"],
    queryFn: getEnvironmentProtectionMenu,
  });
  const EnvironmentModuleRoutes = useMemo(() => {
    return createDynamicModuleRoutes(
      "environment",
      EnvironmentMenuData,
      routerToMenu,
      list
    );
  }, [list, EnvironmentMenuData, routerToMenu]);

  const ContractorModuleRoutes = useMemo(
    () => [
      {
        text: "承包商基础信息",
        itemKey: "contractor_basic",
        items: routerToMenu(ContractorBasicMap, list),
      },
      {
        text: "承包商入场管理",
        itemKey: "contractor_entry",
        items: routerToMenu(ContractorEntryMap, list),
      },
      {
        text: "承包商项目管理",
        itemKey: "contractor_project",
        items: routerToMenu(ContractorProjectMap, list),
      },
      {
        text: "承包商评价管理",
        itemKey: "contractor_evaluation",
        items: routerToMenu(ContractorEvaluationMap, list),
      },
    ],
    [list]
  );

  const AlarmModuleRoutes = useMemo(
    () => [
      {
        text: "报警设置",
        itemKey: "alarm_settings",
        items: routerToMenu(AlarmSettingsMap, list),
      },
      {
        text: "报警处理",
        itemKey: "alarm_process",
        items: routerToMenu(AlarmProcessMap, list),
      },
      {
        text: "报警分析",
        itemKey: "alarm_real_time",
        items: routerToMenu(AlarmAnalysisMap, list),
      },
    ],
    [list]
  );

  // 这里必须按序来，根据MainMenu定义引用了MainMenuAllRoutes[item.bindApiKey], bindApiKey是mainMenuMap中定义的
  const MainMenuAllRoutes = useMemo(
    () => [
      HideMenuRoutes,
      SystemSettingRoutes,
      BaseSettingRoutes,
      DoubleGuardRoutes,
      SpecialWorkRoutes,
      MajorHazardRoutes,
      EmergencyManagementRoutes,
      PersonnelLocationRoutes,
      AIRoutes,
      IntelligentInspectionRoutes,
      TrainingRoutes,
      [], //bigScreen占位符
      EquipmentManagementRoutes,
      FireFighterModuleRoutes,
      EnergyModuleRoutes,
      EnvironmentModuleRoutes,
      ContractorModuleRoutes,
      AlarmModuleRoutes,
    ],
    [
      HideMenuRoutes,
      SystemSettingRoutes,
      BaseSettingRoutes,
      DoubleGuardRoutes,
      SpecialWorkRoutes,
      MajorHazardRoutes,
      EmergencyManagementRoutes,
      PersonnelLocationRoutes,
      AIRoutes,
      IntelligentInspectionRoutes,
      TrainingRoutes,
      EquipmentManagementRoutes,
      FireFighterModuleRoutes,
      EnergyModuleRoutes,
      EnvironmentModuleRoutes,
      ContractorModuleRoutes,
      AlarmModuleRoutes,
    ]
  );

  const hasHide = (list: any): boolean => {
    let count = 0;

    list?.forEach?.((o) => {
      count += o?.items?.length ?? 0;
    });

    if (count >= 1) {
      return false;
    }
    return true;
  };

  const generateItemKey = (module: any[]): string => {
    let key = "";

    module?.forEach((menu) => {
      if (menu.items?.length) {
        if (key === "") {
          key = menu.items[0].itemKey;
        }
      }
    });
    return key;
  };

  const MainMenu = useMemo(
    () => [
      {
        icon: <IconDesktop />,
        items: mainMenuMap
          .map((item, index, array) => {
            if (item?.isInMenu === undefined || item?.isInMenu === true) {
              return {
                ...item,
                itemKey: generateItemKey(MainMenuAllRoutes[item.bindApiKey]),
                module: MainMenuAllRoutes[item.bindApiKey],
                hide: hasHide(MainMenuAllRoutes[item.bindApiKey]),
              };
            }
          })
          .filter((element, index, array) => {
            return (
              platformConfig?.webMenus?.includes(element?.bindApiKey) ||
              element?.bindApiKey === 0
            );
          }),
      },
    ],
    [platformConfig, DoubleGuardRoutes]
  );

  return {
    DoubleGuardRoutes,
    SpecialWorkRoutes,
    HideMenuRoutes,
    BaseSettingRoutes,
    SystemSettingRoutes,
    MajorHazardRoutes,
    EmergencyManagementRoutes,
    PersonnelLocationRoutes,
    AIRoutes,
    IntelligentInspectionRoutes,
    TrainingRoutes,
    MainMenu,
    EquipmentManagementRoutes,
    FireFighterModuleRoutes,
    EnergyModuleRoutes,
    EnvironmentModuleRoutes,
    ContractorModuleRoutes,
    AlarmModuleRoutes,
  };
};

// 新增的辅助函数
const createDynamicModuleRoutes = (
  moduleName: string,
  menuData: any,
  routerToMenuFn: (list: ChildrenMap, roleItem: Roles) => any[],
  currentList: Roles,
  prependStaticItems?: Array<{
    text: string;
    name: string;
    staticId: number | string;
    pathSuffix?: string;
  }>
) => {
  const menuList = menuData?.data?.menuList ?? [];
  const rootMenu: any[] = [];

  if (menuList?.length) {
    menuList.forEach(
      (
        item: {
          name: string;
          subItemList: Array<{
            id: number;
            name: string;
            displayType?: string;
          }>; // Updated type for subItemList
        },
        index: number
      ) => {
        const subMenu: any[] = [];

        if (prependStaticItems?.length) {
          prependStaticItems.forEach((staticItem) => {
            subMenu.push({
              text: staticItem.text,
              itemKey: `${moduleName}_module_sub_${staticItem.staticId}`,
              name: staticItem.name,
              path: `/${moduleName}/${staticItem.pathSuffix !== undefined ? staticItem.pathSuffix : staticItem.staticId}`,
            });
          });
        }

        item?.subItemList?.forEach(
          (subItem: { id: number; name: string; displayType?: string }) => {
            // Updated type for subItem
            let newPath = `/${moduleName}/${subItem.id}`;

            // 分开处理 displayType
            if (subItem.displayType) {
              const displayTypeEncoded = encodeURIComponent(
                subItem.displayType
              );
              // 确保 displayType 编码后非空
              if (displayTypeEncoded) {
                newPath += `/${displayTypeEncoded}`;
              }
            }

            // 分开处理 name, 追加在 displayType 之后 (如果 displayType 存在)
            /* if (subItem.name) {
            const nameEncoded = encodeURIComponent(subItem.name);
            // 确保 name 编码后非空
            if (nameEncoded) {
                 newPath += `/${nameEncoded}`;
            }
          } */

            subMenu.push({
              text: subItem?.name ?? "",
              itemKey: `${moduleName}_module_sub_${subItem.id}`,
              name: subItem?.name ?? "",
              path: newPath, // Use the new path
            });
          }
        );

        rootMenu.push({
          text: item?.name ?? "",
          itemKey: `${moduleName}_module_${index}`,
          items: routerToMenuFn(subMenu, currentList),
        });
      }
    );
  }
  return rootMenu;
};
