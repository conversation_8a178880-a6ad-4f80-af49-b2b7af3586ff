// 消息管理相关路由
// 包含 MessageMap

// 完全复制App.tsx中的导入语句，不做任何修改
import { MessagePage } from "pages/basicInfo";
import { MessageRoute } from "utils/routerConstants";
import type { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

// 完全复制App.tsx中的MessageMap定义，不做任何修改
export const MessageMap: ChildrenMap[] = generateLoader(
  [
    {
      path: MessageRoute, // "/message",
      element: <MessagePage />,
      name: "消息中心",
    },
  ],
  2000
);
