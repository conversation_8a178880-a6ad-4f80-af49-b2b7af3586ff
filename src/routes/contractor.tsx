import { ContractorRoutes } from "utils/routerConstants";
import { ChildrenMap } from "./types";
import { generateLoader } from "./utils";
import { BasicInfoContractorBlacklistAddPage, BasicInfoContractorBlacklistAppealPage, BasicInfoContractorConfigPage, BasicInfoContractorEmployeeBlacklistAddPage, BasicInfoContractorEmployeeBlacklistAppealPage, BasicInfoContractorEntryApplyPage, BasicInfoContractorEvaluationPage, BasicInfoContractorProjectResumptionPage, BasicInfoContractorProjectStopPage, BasicInfoContractorViolationPage, ContractorAccidentPage, ContractorCertificatePage, ContractorEmployeeCertificatePage, ContractorEmployeePage, ContractorPage, ContractorProjectPage } from "pages/basicInfo";
import { CoporateTrainingPlanPage } from "pages/coporateTraining";
import { TicketListPage } from "pages/ticket";

export const ContractorBasicMap: ChildrenMap[] = generateLoader([
  {
    path: ContractorRoutes.CONTRACTOR_CONFIG,
    element: <BasicInfoContractorConfigPage />,
    name: "承包商配置",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR,
    element: <ContractorPage />,
    name: "承包商基础信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "detail",
        name: "查看详情",
      },
      {
        action: "addBlacklist",
        name: "加入黑名单",
      },
      {
        action: "removeBlacklist",
        name: "移出黑名单",
      },
      {
        action: "share",
        name: "邀请完善信息",
      },
      {
        action: "import",
        name: "导入",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_CERTIFICATE,
    element: <ContractorCertificatePage />,
    name: "承包商资证信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE, // "/contractor_employee",
    element: <ContractorEmployeePage />,
    name: "承包商人员信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "addBlacklist",
        name: "加入黑名单",
      },
      {
        action: "removeBlacklist",
        name: "移出黑名单",
      },
      {
        action: "import",
        name: "导入",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE_CERTIFICATE, // "/contractor_employee_certificate",
    element: <ContractorEmployeeCertificatePage />,
    name: "承包商人员证书信息",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "import",
        name: "导入",
      },
    ],
  },
]);

export const ContractorEntryMap: ChildrenMap[] = generateLoader([
  {
    path: ContractorRoutes.CONTRACTOR_ENTRY_TRAINING,
    element: <CoporateTrainingPlanPage filter={{ objectType: 3 }} />,
    name: "承包商培训",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "detail",
        name: "查看详情",
      },
      {
        action: "share",
        name: "分享",
      },
      {
        action: "revokeEntry",
        name: "取消入厂资格",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_ENTRY_APPLY,
    element: <BasicInfoContractorEntryApplyPage />,
    name: "承包商入厂申请",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审核",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
]);

export const ContractorProjectMap: ChildrenMap[] = generateLoader([
  {
    element: (
      <TicketListPage
        filter={{ unitCategory: 2 }} // 承包商
        isInContractorModule={true}
      />
    ),
    name: "承包商作业票",
    path: ContractorRoutes.JOB_TMPL_LIST, // "/job_tmpl_list",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "view",
        name: "查看打印",
      },
      {
        action: "export",
        name: "导出",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_PROJECT, // "/contractor_project",
    element: <ContractorProjectPage />,
    name: "承包商项目管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "stop",
        name: "停工",
      },
      {
        action: "batchStop",
        name: "批量停工",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_ACCIDENT, // "/contractor_accident",
    element: <ContractorAccidentPage />,
    name: "承包商事故事件",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_VIOLATION,
    element: <BasicInfoContractorViolationPage />,
    name: "承包商违规事件",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_PROJECT_STOP,
    element: <BasicInfoContractorProjectStopPage />,
    name: "承包商项目停工记录",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_PROJECT_RESUMPTION,
    element: <BasicInfoContractorProjectResumptionPage />,
    name: "承包商项目复工管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审核",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
]);

export const ContractorEvaluationMap: ChildrenMap[] = generateLoader([
  {
    path: ContractorRoutes.CONTRACTOR_EVALUATION,
    element: <BasicInfoContractorEvaluationPage />,
    name: "承包商业绩评价",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_BLACKLIST_ADD,
    element: <BasicInfoContractorBlacklistAddPage />,
    name: "承包商黑名单记录",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_BLACKLIST_APPEAL,
    element: <BasicInfoContractorBlacklistAppealPage />,
    name: "承包商黑名单申诉管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审批",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE_BLACKLIST_ADD,
    element: <BasicInfoContractorEmployeeBlacklistAddPage />,
    name: "承包商人员黑名单记录",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: ContractorRoutes.CONTRACTOR_EMPLOYEE_BLACKLIST_APPEAL,
    element: <BasicInfoContractorEmployeeBlacklistAppealPage />,
    name: "承包商人员黑名单申诉管理",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
      {
        action: "audit",
        name: "审批",
      },
      {
        action: "auditDelegate",
        name: "审批代办",
      },
    ],
  },
]);
