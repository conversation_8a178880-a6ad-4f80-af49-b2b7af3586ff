// 培训相关路由
// 包含 TrainingMaterialMap, TrainingManagementMap, StudentManagementMap, MyTrainingLayoutMap, TestPaperMap, MyTrainingMap, MyTrainingCertificateMap

import {
  CoporateTrainingCertificatePage,
  CoporateTrainingPeopleExamRecordPage,
  CoporateTrainingPeoplePage,
  CoporateTrainingPeopleStudyRecordPage,
  CoporateTrainingPlanPage,
  CoporateTrainingRecordPage,
  CoursePage,
  CoursewarePage,
  PaperPage,
  QuestionPage,
  TeacherPage,
  TrainingConfigPage,
  TrainingSubjectPage,
} from "pages/coporateTraining";
import { MyTrainingPage, TrainingRecordPage } from "pages/training";
import { MyTrainingCertificatePage } from "pages/training/certificatePage";
import { TrainingCoursePage } from "pages/training/coursePage";
import { TestPaperPage } from "pages/training/testPaperPage";
import { TrainingIndexPage } from "pages/training/trainingIndexPage";
import { TrainingLayoutPage } from "pages/training/trainingPage";
import { TrainingRoutes } from "utils/routerConstants";
import type { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

export const TrainingMaterialMap: ChildrenMap[] = generateLoader(
  [
    {
      path: TrainingRoutes.TRAINING_DASHBOARD, // "/training_config",
      element: <TrainingIndexPage />,
      name: "培训教育",
    },
    {
      path: TrainingRoutes.TRAINING_CONFIG, // "/training_config",
      element: <TrainingConfigPage />,
      name: "学习资料通用配置",
    },
    {
      path: TrainingRoutes.TRAINING_SUBJECT, // "/training_subject",
      element: <TrainingSubjectPage />,
      name: "知识科目分类",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        /*  {
      action: 'removes',
      name: '批量删除'
    }, */ {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_TEACHER, // "/training_teacher",
      element: <TeacherPage />,
      name: "讲师管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_COURSEWARE, // "/training_courseware",
      element: <CoursewarePage />,
      name: "课件库",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "approve",
          name: "通过",
        },
        {
          action: "reject",
          name: "驳回",
        },
        {
          action: "batchApprove",
          name: "批量通过",
        },
        {
          action: "batchReject",
          name: "批量驳回",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_QUESTION, // "/training_question",
      element: <QuestionPage />,
      name: "试题库",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "approve",
          name: "通过",
        },
        {
          action: "reject",
          name: "驳回",
        },
        {
          action: "batchApprove",
          name: "批量通过",
        },
        {
          action: "batchReject",
          name: "批量驳回",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_PAPER, // "/training_paper",
      element: <PaperPage />,
      name: "试卷管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_COURSE, // "/training_course",
      element: <CoursePage />,
      name: "课程管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
  ],
  600
);

export const TrainingManagementMap: ChildrenMap[] = generateLoader(
  [
    {
      path: TrainingRoutes.TRAINING_CERTIFICATE, // "/training_certificate",
      element: <CoporateTrainingCertificatePage />,
      name: "培训证书管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_PLAN, // "/training_plan",
      element: <CoporateTrainingPlanPage />,
      name: "培训计划",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "approve",
          name: "审批",
        },
        {
          action: "grantQualification",
          name: "授予入厂资格",
        },
        {
          action: "cancelQualification",
          name: "取消入厂资格",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_RECORD, // "/training_record",
      element: <CoporateTrainingRecordPage />,
      name: "培训记录",
      meta: [
        {
          action: "export",
          name: "导出",
        },
        {
          action: "detail",
          name: "详情",
        },
      ],
    },
  ],
  600
);

export const StudentManagementMap: ChildrenMap[] = generateLoader(
  [
    {
      path: TrainingRoutes.PEOPLE_STUDY_RECORD, // "/people_study_record",
      element: <CoporateTrainingPeopleStudyRecordPage />,
      name: "学习记录",
    },
    {
      path: TrainingRoutes.PEOPLE_EXAM_RECORD, // "/people_exam_record",
      element: <CoporateTrainingPeopleExamRecordPage />,
      name: "考试记录",
      meta: [
        {
          action: "detail",
          name: "详情",
        },
      ],
    },
    {
      path: TrainingRoutes.PEOPLE, // "/people",
      element: <CoporateTrainingPeoplePage />,
      name: "学员培训档案",
    },
  ],
  600
);

export const TestPaperMap: ChildrenMap[] = generateLoader(
  [
    {
      index: true,
      path: "test_paper",
      element: <TestPaperPage />,
      name: "考试",
    },
  ],
  600
);

export const MyTrainingLayoutMap: ChildrenMap[] = generateLoader(
  [
    {
      // TODO: 为什么这里的path不生效
      path: TrainingRoutes.MY_TRAINING, // "/my_training",
      // path: "/my_training",
      element: <TrainingLayoutPage />,
      name: "我的培训",
      handle: {
        crumb: {
          history: {
            name: "我的培训",
            path: "/my_training",
          },
        },
      },
      children: [
        {
          index: true,
          element: <TrainingLayoutPage />,
          name: "我的培训",
        },
        {
          path: "training_record/:record_id/course/:id",
          element: <TrainingCoursePage />,
          name: "课程详情",
          handle: {
            crumb: {
              history: {
                name: "培训详情",
                path: "/my_training",
              },
            },
          },
        },
      ],
    },
  ],
  600
);

export const MyTrainingMap: ChildrenMap[] = generateLoader(
  [
    {
      index: true,
      element: <MyTrainingPage />,
      name: "培训计划",
    },
    {
      path: "training_record/:record_id",
      element: <TrainingRecordPage />,
      name: "培训详情",
      handle: {
        crumb: {
          name: "培训详情",
        },
      },
    },
    {
      path: "training_record/:record_id/course/:id",
      element: <TrainingCoursePage />,
      name: "课程详情",
      handle: {
        crumb: {
          history: {
            name: "培训详情",
            path: "/my_training/training_record/",
            params: "record_id",
          },
          name: "课程详情",
        },
      },
    },
  ],
  600
);

export const MyTrainingCertificateMap: ChildrenMap[] = generateLoader(
  [
    {
      index: true,
      path: "certificate",
      element: <MyTrainingCertificatePage />,
      name: "培训证书",
    },
  ],
  600
);
